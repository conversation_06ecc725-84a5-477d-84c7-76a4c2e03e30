#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import xml.etree.ElementTree as ET
from xml.dom import minidom

def create_kml_from_csv():
    """将CSV文件转换为KML格式，按交通系统分组"""
    
    # 读取CSV文件
    stations = []
    try:
        with open('台湾交通车站统一信息表.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                stations.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '台湾交通车站统一信息表.csv'")
        return
    
    # 按交通系统分组
    systems = {}
    for station in stations:
        system = station['交通系统']
        if system not in systems:
            systems[system] = []
        systems[system].append(station)
    
    # 创建KML根元素
    kml = ET.Element('kml', xmlns="http://www.opengis.net/kml/2.2")
    document = ET.SubElement(kml, 'Document')
    
    # 添加文档信息
    name = ET.SubElement(document, 'name')
    name.text = '台湾交通车站统一信息'
    
    description = ET.SubElement(document, 'description')
    description.text = f'包含台湾高铁、台铁和捷运系统的{len(stations)}个车站信息'
    
    # 定义样式
    styles = {
        '高铁': {'color': 'ff0000ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/rail.png'},  # 红色
        '台铁': {'color': 'ff00ff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/train.png'},  # 绿色
        '捷运': {'color': 'ffff0000', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/subway.png'}   # 蓝色
    }
    
    # 添加样式定义
    for system, style_info in styles.items():
        style = ET.SubElement(document, 'Style', id=f'{system}_style')
        icon_style = ET.SubElement(style, 'IconStyle')
        color = ET.SubElement(icon_style, 'color')
        color.text = style_info['color']
        scale = ET.SubElement(icon_style, 'scale')
        scale.text = '1.2'
        icon = ET.SubElement(icon_style, 'Icon')
        href = ET.SubElement(icon, 'href')
        href.text = style_info['icon']
        
        # 标签样式
        label_style = ET.SubElement(style, 'LabelStyle')
        label_color = ET.SubElement(label_style, 'color')
        label_color.text = style_info['color']
        label_scale = ET.SubElement(label_style, 'scale')
        label_scale.text = '0.8'
    
    # 为每个交通系统创建文件夹
    for system, system_stations in systems.items():
        folder = ET.SubElement(document, 'Folder')
        
        folder_name = ET.SubElement(folder, 'name')
        folder_name.text = f'{system}车站 ({len(system_stations)}个)'
        
        folder_desc = ET.SubElement(folder, 'description')
        folder_desc.text = f'{system}系统的所有车站位置信息'
        
        # 添加该系统的所有车站
        for station in system_stations:
            placemark = ET.SubElement(folder, 'Placemark')
            
            # 车站名称
            placemark_name = ET.SubElement(placemark, 'name')
            placemark_name.text = station['车站名称']
            
            # 详细描述
            placemark_desc = ET.SubElement(placemark, 'description')
            desc_html = f"""
            <![CDATA[
            <table border="1" cellpadding="5" cellspacing="0">
                <tr><td><b>车站编号</b></td><td>{station['车站编号']}</td></tr>
                <tr><td><b>车站名称</b></td><td>{station['车站名称']}</td></tr>
                <tr><td><b>英文名称</b></td><td>{station['英文名称']}</td></tr>
                <tr><td><b>交通系统</b></td><td>{station['交通系统']}</td></tr>
                <tr><td><b>路线名称</b></td><td>{station['路线名称']}</td></tr>
                <tr><td><b>地址</b></td><td>{station['地址']}</td></tr>
                <tr><td><b>电话</b></td><td>{station['电话']}</td></tr>
                <tr><td><b>坐标</b></td><td>{station['经度']}, {station['纬度']}</td></tr>
                <tr><td><b>备注</b></td><td>{station['备注']}</td></tr>
            </table>
            ]]>
            """
            placemark_desc.text = desc_html
            
            # 应用样式
            style_url = ET.SubElement(placemark, 'styleUrl')
            style_url.text = f'#{system}_style'
            
            # 坐标点
            point = ET.SubElement(placemark, 'Point')
            coordinates = ET.SubElement(point, 'coordinates')
            coordinates.text = f"{station['经度']},{station['纬度']},0"
    
    # 格式化XML并保存
    rough_string = ET.tostring(kml, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    pretty_xml = reparsed.toprettyxml(indent="  ", encoding='utf-8')
    
    # 保存KML文件
    output_file = '台湾交通车站统一信息.kml'
    with open(output_file, 'wb') as f:
        f.write(pretty_xml)
    
    print(f"✅ 成功创建KML文件: {output_file}")
    print(f"📊 包含 {len(stations)} 个车站，分为 {len(systems)} 个分组：")
    for system, system_stations in systems.items():
        print(f"   - {system}：{len(system_stations)} 个车站")

def create_simple_kml():
    """创建简化版本的KML（如果XML库有问题）"""
    
    # 读取CSV文件
    stations = []
    try:
        with open('台湾交通车站统一信息表.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                stations.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '台湾交通车站统一信息表.csv'")
        return
    
    # 按交通系统分组
    systems = {}
    for station in stations:
        system = station['交通系统']
        if system not in systems:
            systems[system] = []
        systems[system].append(station)
    
    # 手动构建KML字符串
    kml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>台湾交通车站统一信息</name>
    <description>包含台湾高铁、台铁和捷运系统的车站信息</description>
    
    <!-- 样式定义 -->
    <Style id="高铁_style">
      <IconStyle>
        <color>ff0000ff</color>
        <scale>1.2</scale>
        <Icon>
          <href>http://maps.google.com/mapfiles/kml/shapes/rail.png</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>ff0000ff</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>
    
    <Style id="台铁_style">
      <IconStyle>
        <color>ff00ff00</color>
        <scale>1.2</scale>
        <Icon>
          <href>http://maps.google.com/mapfiles/kml/shapes/train.png</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>ff00ff00</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>
    
    <Style id="捷运_style">
      <IconStyle>
        <color>ffff0000</color>
        <scale>1.2</scale>
        <Icon>
          <href>http://maps.google.com/mapfiles/kml/shapes/subway.png</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>ffff0000</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>
'''
    
    # 为每个交通系统添加文件夹
    for system, system_stations in systems.items():
        kml_content += f'''
    <Folder>
      <name>{system}车站 ({len(system_stations)}个)</name>
      <description>{system}系统的所有车站位置信息</description>
'''
        
        # 添加该系统的所有车站
        for station in system_stations:
            kml_content += f'''
      <Placemark>
        <name>{station['车站名称']}</name>
        <description><![CDATA[
          <table border="1" cellpadding="5" cellspacing="0">
            <tr><td><b>车站编号</b></td><td>{station['车站编号']}</td></tr>
            <tr><td><b>车站名称</b></td><td>{station['车站名称']}</td></tr>
            <tr><td><b>英文名称</b></td><td>{station['英文名称']}</td></tr>
            <tr><td><b>交通系统</b></td><td>{station['交通系统']}</td></tr>
            <tr><td><b>路线名称</b></td><td>{station['路线名称']}</td></tr>
            <tr><td><b>地址</b></td><td>{station['地址']}</td></tr>
            <tr><td><b>电话</b></td><td>{station['电话']}</td></tr>
            <tr><td><b>坐标</b></td><td>{station['经度']}, {station['纬度']}</td></tr>
            <tr><td><b>备注</b></td><td>{station['备注']}</td></tr>
          </table>
        ]]></description>
        <styleUrl>#{system}_style</styleUrl>
        <Point>
          <coordinates>{station['经度']},{station['纬度']},0</coordinates>
        </Point>
      </Placemark>'''
        
        kml_content += '''
    </Folder>'''
    
    kml_content += '''
  </Document>
</kml>'''
    
    # 保存KML文件
    output_file = '台湾交通车站统一信息.kml'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(kml_content)
    
    print(f"✅ 成功创建KML文件: {output_file}")
    print(f"📊 包含 {len(stations)} 个车站，分为 {len(systems)} 个分组：")
    for system, system_stations in systems.items():
        print(f"   - {system}：{len(system_stations)} 个车站")

if __name__ == "__main__":
    try:
        create_kml_from_csv()
    except Exception as e:
        print(f"使用XML库时出错: {e}")
        print("尝试使用简化方法...")
        create_simple_kml()
