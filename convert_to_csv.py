#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import csv

def convert_json_to_csv():
    # 读取JSON文件
    try:
        with open('台湾高速铁路（高铁）车站.txt', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("错误：找不到文件 '台湾高速铁路（高铁）车站.txt'")
        return
    except json.JSONDecodeError:
        print("错误：JSON文件格式不正确")
        return

    # 创建CSV文件
    with open('台湾捷运车站表格.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = ['车站编号', '中文站名', '英译站名', '路线编号', '路线名', '地址', '经度', '纬度']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # 写入表头
        writer.writeheader()
        
        # 写入数据
        for feature in data['features']:
            props = feature['properties']
            coords = feature['geometry']['coordinates']
            
            row = {
                '车站编号': props['車站編號'],
                '中文站名': props['中文站名'],
                '英译站名': props['英譯站名'],
                '路线编号': props['路線編號'],
                '路线名': props['路線名'],
                '地址': props['地址'],
                '经度': coords[0],
                '纬度': coords[1]
            }
            writer.writerow(row)
    
    print(f'已成功转换 {len(data["features"])} 个车站信息到CSV文件: 台湾捷运车站表格.csv')
    
    # 同时创建一个简化的表格文件
    with open('台湾捷运车站简表.txt', 'w', encoding='utf-8') as f:
        f.write("台湾捷运车站信息表\n")
        f.write("=" * 80 + "\n\n")
        
        # 按路线分组
        lines = {}
        for feature in data['features']:
            props = feature['properties']
            line_name = props['路線名']
            if line_name not in lines:
                lines[line_name] = []
            lines[line_name].append(props)
        
        # 输出每条路线的车站
        for line_name, stations in lines.items():
            f.write(f"{line_name} ({len(stations)}个车站)\n")
            f.write("-" * 40 + "\n")
            for station in stations:
                f.write(f"{station['車站編號']:6} {station['中文站名']:12} {station['英譯站名']}\n")
            f.write("\n")
    
    print("同时创建了简化表格文件: 台湾捷运车站简表.txt")

if __name__ == "__main__":
    convert_json_to_csv()
