#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import pandas as pd

def convert_tra_to_csv():
    """将台铁车站数据转换为更好的CSV格式"""
    
    # 读取原始CSV文件
    try:
        df = pd.read_csv('铁路（台铁）车站.txt', encoding='utf-8')
    except FileNotFoundError:
        print("错误：找不到文件 '铁路（台铁）车站.txt'")
        return
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return
    
    # 重命名列名为中文
    df_chinese = df.rename(columns={
        'ID': '车站ID',
        'TIMETABLE_ID': '时刻表ID', 
        'NAME': '车站名称',
        'BRANCH': '支线',
        'ADDRESS': '地址',
        'TEL': '电话',
        'LAT': '纬度',
        'LNG': '经度'
    })
    
    # 处理支线信息，空值用"主线"替代
    df_chinese['支线'] = df_chinese['支线'].fillna('主线')
    
    # 处理电话信息，空值用"无"替代
    df_chinese['电话'] = df_chinese['电话'].fillna('无')
    
    # 保存为新的CSV文件
    output_file = '台铁车站信息表.csv'
    df_chinese.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f'已成功转换 {len(df_chinese)} 个台铁车站信息到CSV文件: {output_file}')
    
    # 创建统计信息
    branch_stats = df_chinese['支线'].value_counts()
    
    # 创建简化的文本报告
    with open('台铁车站统计报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾铁路（台铁）车站信息统计报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总车站数：{len(df_chinese)} 个\n\n")
        
        f.write("按支线分类统计：\n")
        f.write("-" * 30 + "\n")
        for branch, count in branch_stats.items():
            f.write(f"{branch:12} {count:3} 个车站\n")
        
        f.write("\n\n各支线车站详细列表：\n")
        f.write("=" * 50 + "\n")
        
        for branch in branch_stats.index:
            f.write(f"\n{branch} ({branch_stats[branch]}个车站)\n")
            f.write("-" * 40 + "\n")
            
            branch_stations = df_chinese[df_chinese['支线'] == branch].sort_values('车站ID')
            for _, station in branch_stations.iterrows():
                f.write(f"{station['车站ID']:3} {station['车站名称']:12} {station['地址']}\n")
    
    print("同时创建了统计报告文件: 台铁车站统计报告.txt")
    
    # 创建HTML表格
    create_html_table(df_chinese)

def create_html_table(df):
    """创建HTML表格文件"""
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台湾铁路（台铁）车站信息表</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }}
        .stats {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 4px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }}
        .stat-item {{
            text-align: center;
            margin: 5px;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
            position: sticky;
            top: 0;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .station-id {{
            font-weight: bold;
            color: #666;
            text-align: center;
        }}
        .coordinates {{
            font-family: monospace;
            font-size: 0.9em;
        }}
        .branch-main {{ color: #2c5aa0; font-weight: bold; }}
        .branch-mountain {{ color: #8b4513; }}
        .branch-other {{ color: #008000; }}
        .search-box {{
            margin-bottom: 20px;
            padding: 10px;
            width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>台湾铁路（台铁）车站信息表</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{len(df)}</div>
                <div>总车站数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{len(df[df['支线'] == '主线'])}</div>
                <div>主线车站</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{len(df[df['支线'] == '山'])}</div>
                <div>山线车站</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{len(df[df['支线'].isin(['集集線', '平溪線', '內灣線', '六家線', '沙崙線'])])}</div>
                <div>支线车站</div>
            </div>
        </div>
        
        <input type="text" id="searchBox" class="search-box" placeholder="搜索车站名称、地址或支线...">
        
        <table id="stationTable">
            <thead>
                <tr>
                    <th>车站ID</th>
                    <th>车站名称</th>
                    <th>支线</th>
                    <th>地址</th>
                    <th>电话</th>
                    <th>纬度</th>
                    <th>经度</th>
                </tr>
            </thead>
            <tbody id="tableBody">
"""
    
    # 添加表格数据
    for _, row in df.iterrows():
        branch_class = get_branch_class(row['支线'])
        html_content += f"""                <tr>
                    <td class="station-id">{row['车站ID']}</td>
                    <td>{row['车站名称']}</td>
                    <td class="{branch_class}">{row['支线']}</td>
                    <td>{row['地址']}</td>
                    <td>{row['电话']}</td>
                    <td class="coordinates">{row['纬度']:.6f}</td>
                    <td class="coordinates">{row['经度']:.6f}</td>
                </tr>
"""
    
    html_content += """            </tbody>
        </table>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchBox').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#tableBody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>"""
    
    with open('台铁车站信息表.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("同时创建了HTML表格文件: 台铁车站信息表.html")

def get_branch_class(branch):
    """根据支线类型返回CSS类名"""
    if branch == '主线':
        return 'branch-main'
    elif branch == '山':
        return 'branch-mountain'
    else:
        return 'branch-other'

if __name__ == "__main__":
    try:
        convert_tra_to_csv()
    except ImportError:
        print("需要安装pandas库：pip install pandas")
        # 如果没有pandas，使用基础方法
        print("使用基础方法处理...")
        convert_tra_basic()

def convert_tra_basic():
    """不使用pandas的基础转换方法"""
    with open('铁路（台铁）车站.txt', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        rows = list(reader)
    
    # 创建中文表头
    chinese_header = ['车站ID', '时刻表ID', '车站名称', '支线', '地址', '电话', '纬度', '经度']
    
    with open('台铁车站信息表.csv', 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(chinese_header)
        writer.writerows(rows)
    
    print(f'已成功转换 {len(rows)} 个台铁车站信息到CSV文件')
