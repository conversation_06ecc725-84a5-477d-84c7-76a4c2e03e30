#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import json

def unify_station_tables():
    """统一三个车站表格的列结构"""
    
    # 定义统一的列结构
    unified_columns = [
        '车站编号',      # 统一的车站编号
        '车站名称',      # 统一的车站名称
        '英文名称',      # 英文名称（如果有）
        '交通系统',      # 高铁/台铁/捷运
        '路线名称',      # 路线或支线名称
        '地址',         # 完整地址
        '电话',         # 联系电话
        '经度',         # 经度坐标
        '纬度',         # 纬度坐标
        '备注'          # 其他信息
    ]
    
    all_stations = []
    
    # 处理台铁车站数据
    print("处理台铁车站数据...")
    try:
        with open('台铁车站信息表.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                station = {
                    '车站编号': row['车站ID'],
                    '车站名称': row['车站名称'],
                    '英文名称': '',  # 台铁数据中没有英文名
                    '交通系统': '台铁',
                    '路线名称': row['支线'] if row['支线'] != '主线' else '西部干线',
                    '地址': row['地址'],
                    '电话': row['电话'],
                    '经度': float(row['经度']),
                    '纬度': float(row['纬度']),
                    '备注': f"时刻表ID: {row['时刻表ID']}"
                }
                all_stations.append(station)
        print(f"台铁车站: {len([s for s in all_stations if s['交通系统'] == '台铁'])} 个")
    except FileNotFoundError:
        print("未找到台铁车站信息表.csv")
    
    # 处理高铁车站数据
    print("处理高铁车站数据...")
    try:
        with open('台湾高铁车站信息表.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                station = {
                    '车站编号': row['车站编号'],
                    '车站名称': row['车站名称'],
                    '英文名称': '',  # 高铁数据中没有英文名
                    '交通系统': '高铁',
                    '路线名称': '台湾高速铁路',
                    '地址': row['地址'],
                    '电话': '',  # 高铁数据中没有电话
                    '经度': float(row['经度']),
                    '纬度': float(row['纬度']),
                    '备注': f"邮政编码: {row['邮政编码']}"
                }
                all_stations.append(station)
        print(f"高铁车站: {len([s for s in all_stations if s['交通系统'] == '高铁'])} 个")
    except FileNotFoundError:
        print("未找到台湾高铁车站信息表.csv")
    
    # 处理捷运车站数据
    print("处理捷运车站数据...")
    try:
        with open('台湾捷运车站表格.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                station = {
                    '车站编号': row['车站编号'],
                    '车站名称': row['中文站名'],
                    '英文名称': row['英译站名'],
                    '交通系统': '捷运',
                    '路线名称': row['路线名'],
                    '地址': row['地址'],
                    '电话': '',  # 捷运数据中没有电话
                    '经度': float(row['经度']),
                    '纬度': float(row['纬度']),
                    '备注': f"路线编号: {row['路线编号']}"
                }
                all_stations.append(station)
        print(f"捷运车站: {len([s for s in all_stations if s['交通系统'] == '捷运'])} 个")
    except FileNotFoundError:
        print("未找到台湾捷运车站表格.csv")
    
    # 保存统一的CSV文件
    output_file = '台湾交通车站统一信息表.csv'
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=unified_columns)
        writer.writeheader()
        writer.writerows(all_stations)
    
    print(f"\n已成功创建统一表格: {output_file}")
    print(f"总计车站数: {len(all_stations)} 个")
    
    # 创建统计报告
    create_unified_report(all_stations)
    
    # 创建HTML表格
    create_unified_html_table(all_stations)
    
    # 创建JSON文件
    with open('台湾交通车站统一信息.json', 'w', encoding='utf-8') as f:
        json.dump(all_stations, f, ensure_ascii=False, indent=2)
    
    return all_stations

def create_unified_report(stations):
    """创建统一的统计报告"""
    with open('台湾交通车站统一报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾交通车站统一信息报告\n")
        f.write("=" * 60 + "\n\n")
        
        # 按交通系统统计
        systems = {}
        for station in stations:
            system = station['交通系统']
            if system not in systems:
                systems[system] = []
            systems[system].append(station)
        
        f.write("按交通系统统计：\n")
        f.write("-" * 30 + "\n")
        for system, system_stations in systems.items():
            f.write(f"{system:6} {len(system_stations):4} 个车站\n")
        
        f.write(f"\n总计：{len(stations)} 个车站\n\n")
        
        # 详细列表
        for system, system_stations in systems.items():
            f.write(f"\n{system}车站详细列表 ({len(system_stations)}个)\n")
            f.write("=" * 50 + "\n")
            
            # 按路线分组
            lines = {}
            for station in system_stations:
                line = station['路线名称']
                if line not in lines:
                    lines[line] = []
                lines[line].append(station)
            
            for line, line_stations in lines.items():
                f.write(f"\n{line} ({len(line_stations)}个车站)\n")
                f.write("-" * 40 + "\n")
                for station in line_stations:
                    f.write(f"{station['车站编号']:6} {station['车站名称']:15} ")
                    if station['英文名称']:
                        f.write(f"{station['英文名称']:25} ")
                    f.write(f"{station['经度']:10.6f} {station['纬度']:10.6f}\n")
    
    print("创建了统一报告文件: 台湾交通车站统一报告.txt")

def create_unified_html_table(stations):
    """创建统一的HTML表格"""
    
    # 统计信息
    systems_count = {}
    for station in stations:
        system = station['交通系统']
        systems_count[system] = systems_count.get(system, 0) + 1
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台湾交通车站统一信息表</title>
    <style>
        body {{
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }}
        h1 {{
            text-align: center;
            color: #2d3436;
            margin-bottom: 20px;
            font-size: 2.8em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }}
        .subtitle {{
            text-align: center;
            color: #636e72;
            margin-bottom: 30px;
            font-size: 1.3em;
        }}
        .stats-container {{
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 10px;
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(0);
            transition: transform 0.3s ease;
        }}
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 1.1em;
        }}
        .controls {{
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }}
        .search-box {{
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            flex: 1;
            min-width: 250px;
            transition: border-color 0.3s ease;
        }}
        .search-box:focus {{
            border-color: #0984e3;
        }}
        .filter-select {{
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            background: white;
            cursor: pointer;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        tr:hover {{
            background-color: #e3f2fd;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }}
        .system-hsr {{ color: #e74c3c; font-weight: bold; }}
        .system-tra {{ color: #27ae60; font-weight: bold; }}
        .system-mrt {{ color: #3498db; font-weight: bold; }}
        .station-id {{
            font-weight: bold;
            text-align: center;
            font-family: 'Courier New', monospace;
        }}
        .coordinates {{
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #666;
        }}
        .english-name {{
            color: #666;
            font-style: italic;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #636e72;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚊 台湾交通车站统一信息表</h1>
        <div class="subtitle">Taiwan Transportation Stations Unified Database</div>
        
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">{len(stations)}</div>
                <div class="stat-label">总车站数</div>
            </div>"""
    
    for system, count in systems_count.items():
        html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{count}</div>
                <div class="stat-label">{system}车站</div>
            </div>"""
    
    html_content += f"""
        </div>
        
        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="🔍 搜索车站名称、地址或路线...">
            <select id="systemFilter" class="filter-select">
                <option value="">所有交通系统</option>
                <option value="高铁">高铁</option>
                <option value="台铁">台铁</option>
                <option value="捷运">捷运</option>
            </select>
        </div>
        
        <table id="stationTable">
            <thead>
                <tr>
                    <th>车站编号</th>
                    <th>车站名称</th>
                    <th>英文名称</th>
                    <th>交通系统</th>
                    <th>路线名称</th>
                    <th>地址</th>
                    <th>电话</th>
                    <th>经度</th>
                    <th>纬度</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody id="tableBody">
"""
    
    # 添加表格数据
    for station in stations:
        system_class = f"system-{{'高铁': 'hsr', '台铁': 'tra', '捷运': 'mrt'}.get(station['交通系统'], 'other')}"
        html_content += f"""                <tr data-system="{station['交通系统']}">
                    <td class="station-id">{station['车站编号']}</td>
                    <td>{station['车站名称']}</td>
                    <td class="english-name">{station['英文名称']}</td>
                    <td class="{system_class}">{station['交通系统']}</td>
                    <td>{station['路线名称']}</td>
                    <td>{station['地址']}</td>
                    <td>{station['电话']}</td>
                    <td class="coordinates">{station['经度']:.6f}</td>
                    <td class="coordinates">{station['纬度']:.6f}</td>
                    <td>{station['备注']}</td>
                </tr>
"""
    
    html_content += """            </tbody>
        </table>
        
        <div class="footer">
            <p>💡 提示：可以使用搜索框和筛选器快速查找特定车站</p>
            <p>数据包含台湾高铁、台铁和捷运系统的所有主要车站信息</p>
        </div>
    </div>

    <script>
        // 搜索和筛选功能
        function filterTable() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const systemFilter = document.getElementById('systemFilter').value;
            const rows = document.querySelectorAll('#tableBody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const system = row.dataset.system;
                
                const matchesSearch = text.includes(searchTerm);
                const matchesSystem = !systemFilter || system === systemFilter;
                
                if (matchesSearch && matchesSystem) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 更新显示的车站数量
            const totalCard = document.querySelector('.stat-card .stat-number');
            if (searchTerm || systemFilter) {
                totalCard.textContent = visibleCount;
                totalCard.parentElement.querySelector('.stat-label').textContent = '搜索结果';
            } else {
                totalCard.textContent = """ + str(len(stations)) + """;
                totalCard.parentElement.querySelector('.stat-label').textContent = '总车站数';
            }
        }
        
        document.getElementById('searchBox').addEventListener('input', filterTable);
        document.getElementById('systemFilter').addEventListener('change', filterTable);
        
        // 表格行点击效果
        document.querySelectorAll('#tableBody tr').forEach(row => {
            row.addEventListener('click', function() {
                console.log('点击了车站：', this.cells[1].textContent);
            });
        });
    </script>
</body>
</html>"""
    
    with open('台湾交通车站统一信息表.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("创建了统一HTML表格文件: 台湾交通车站统一信息表.html")

if __name__ == "__main__":
    stations = unify_station_tables()
    print(f"\n✅ 统一处理完成！")
    print(f"📊 总计处理了 {len(stations)} 个车站")
    print(f"📁 生成的文件：")
    print(f"   - 台湾交通车站统一信息表.csv")
    print(f"   - 台湾交通车站统一信息表.html") 
    print(f"   - 台湾交通车站统一报告.txt")
    print(f"   - 台湾交通车站统一信息.json")
