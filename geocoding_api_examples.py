#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json

class GeocodingService:
    """地理编码服务类"""
    
    def __init__(self):
        # 在这里设置您的API密钥
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.baidu_api_key = "YOUR_BAIDU_API_KEY"
        self.amap_api_key = "YOUR_AMAP_API_KEY"
        self.mapbox_api_key = "YOUR_MAPBOX_API_KEY"
    
    def google_geocode(self, address):
        """使用Google Maps Geocoding API"""
        if self.google_api_key == "YOUR_GOOGLE_API_KEY":
            print("请设置Google API密钥")
            return None, None
            
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'key': self.google_api_key,
            'language': 'zh-TW'  # 繁体中文
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                return location['lng'], location['lat']
            else:
                print(f"Google API错误: {data.get('status', 'Unknown error')}")
                return None, None
                
        except Exception as e:
            print(f"Google API请求失败: {e}")
            return None, None
    
    def baidu_geocode(self, address):
        """使用百度地图API"""
        if self.baidu_api_key == "YOUR_BAIDU_API_KEY":
            print("请设置百度API密钥")
            return None, None
            
        url = "https://api.map.baidu.com/geocoding/v3/"
        params = {
            'address': address,
            'ak': self.baidu_api_key,
            'output': 'json'
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 0:
                location = data['result']['location']
                return location['lng'], location['lat']
            else:
                print(f"百度API错误: {data.get('message', 'Unknown error')}")
                return None, None
                
        except Exception as e:
            print(f"百度API请求失败: {e}")
            return None, None
    
    def amap_geocode(self, address):
        """使用高德地图API"""
        if self.amap_api_key == "YOUR_AMAP_API_KEY":
            print("请设置高德API密钥")
            return None, None
            
        url = "https://restapi.amap.com/v3/geocode/geo"
        params = {
            'address': address,
            'key': self.amap_api_key,
            'output': 'json'
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == '1' and data['geocodes']:
                location = data['geocodes'][0]['location'].split(',')
                return float(location[0]), float(location[1])  # 高德返回的是lng,lat
            else:
                print(f"高德API错误: {data.get('info', 'Unknown error')}")
                return None, None
                
        except Exception as e:
            print(f"高德API请求失败: {e}")
            return None, None
    
    def nominatim_geocode(self, address):
        """使用OpenStreetMap Nominatim (免费)"""
        url = "https://nominatim.openstreetmap.org/search"
        params = {
            'q': address,
            'format': 'json',
            'limit': 1,
            'accept-language': 'zh-TW,zh-CN,en'
        }
        
        headers = {
            'User-Agent': 'Taiwan-Government-Institutions-Geocoder/1.0'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers)
            data = response.json()
            
            if data:
                return float(data[0]['lon']), float(data[0]['lat'])
            else:
                print(f"Nominatim未找到地址: {address}")
                return None, None
                
        except Exception as e:
            print(f"Nominatim请求失败: {e}")
            return None, None
    
    def mapbox_geocode(self, address):
        """使用MapBox Geocoding API"""
        if self.mapbox_api_key == "YOUR_MAPBOX_API_KEY":
            print("请设置MapBox API密钥")
            return None, None
            
        url = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{address}.json"
        params = {
            'access_token': self.mapbox_api_key,
            'language': 'zh',
            'limit': 1
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['features']:
                coordinates = data['features'][0]['geometry']['coordinates']
                return coordinates[0], coordinates[1]  # MapBox返回[lng, lat]
            else:
                print(f"MapBox未找到地址: {address}")
                return None, None
                
        except Exception as e:
            print(f"MapBox请求失败: {e}")
            return None, None

def test_geocoding_services():
    """测试不同的地理编码服务"""
    geocoder = GeocodingService()
    test_address = "100台北市中正區徐州路5號"  # 内政部地址
    
    print(f"测试地址: {test_address}")
    print("-" * 50)
    
    # 测试免费的Nominatim服务
    print("1. 测试 OpenStreetMap Nominatim (免费):")
    lng, lat = geocoder.nominatim_geocode(test_address)
    if lng and lat:
        print(f"   坐标: {lng:.6f}, {lat:.6f}")
    else:
        print("   获取失败")
    time.sleep(1)  # 避免请求过快
    
    # 其他服务需要API密钥
    print("\n2. Google Maps API (需要API密钥)")
    print("3. 百度地图API (需要API密钥)")
    print("4. 高德地图API (需要API密钥)")
    print("5. MapBox API (需要API密钥)")
    
    print("\n" + "="*50)
    print("API申请指南:")
    print("="*50)
    print("Google Maps API:")
    print("  1. 访问 https://console.cloud.google.com/")
    print("  2. 创建项目并启用 Geocoding API")
    print("  3. 创建API密钥")
    print("  4. 设置使用限制和计费")
    
    print("\n百度地图API:")
    print("  1. 访问 https://lbsyun.baidu.com/")
    print("  2. 注册开发者账户")
    print("  3. 创建应用获取AK")
    print("  4. 个人开发者有免费配额")
    
    print("\n高德地图API:")
    print("  1. 访问 https://lbs.amap.com/")
    print("  2. 注册开发者账户")
    print("  3. 创建应用获取Key")
    print("  4. 个人开发者有免费配额")

def create_enhanced_kml_with_api():
    """使用真实API创建增强版KML"""
    import csv
    
    geocoder = GeocodingService()
    
    # 读取CSV文件
    institutions = []
    try:
        with open('综合.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['機構中文全名'].strip() and row['地址'].strip():
                    institutions.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '综合.csv'")
        return
    
    print(f"准备为 {len(institutions)} 个机构获取精确坐标...")
    print("注意：这需要有效的API密钥")
    
    # 为每个机构获取精确坐标
    success_count = 0
    for i, inst in enumerate(institutions):
        print(f"处理 {i+1}/{len(institutions)}: {inst['機構中文全名']}")
        
        # 首先尝试免费的Nominatim
        lng, lat = geocoder.nominatim_geocode(inst['地址'])
        
        if lng and lat:
            inst['经度'] = lng
            inst['纬度'] = lat
            success_count += 1
        else:
            # 如果失败，使用默认坐标
            inst['经度'] = 121.5654  # 台北市中心
            inst['纬度'] = 25.0330
        
        # 避免请求过快
        time.sleep(1)
    
    print(f"成功获取 {success_count}/{len(institutions)} 个精确坐标")
    
    # 保存结果到新的CSV文件
    with open('政府机构_精确坐标.csv', 'w', newline='', encoding='utf-8-sig') as f:
        fieldnames = ['機構中文全名', '機構英文名稱', '層級/類別', '地址', '上級機關', '经度', '纬度']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(institutions)
    
    print("已保存精确坐标到: 政府机构_精确坐标.csv")

if __name__ == "__main__":
    print("地理编码API服务测试")
    print("="*50)
    
    choice = input("选择操作:\n1. 测试API服务\n2. 使用API获取精确坐标\n请输入选择 (1 或 2): ")
    
    if choice == "1":
        test_geocoding_services()
    elif choice == "2":
        create_enhanced_kml_with_api()
    else:
        print("无效选择")
