#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import requests
import time
import json

def geocode_address(address):
    """使用地理编码服务获取地址的经纬度坐标"""
    # 这里使用一个简单的地理编码方法
    # 实际使用时可能需要API密钥
    
    # 简化处理：从地址中提取城市信息并返回大概坐标
    city_coords = {
        '台北市': (121.5654, 25.0330),
        '新北市': (121.4627, 25.0148),
        '桃园市': (121.3015, 24.9937),
        '台中市': (120.6736, 24.1477),
        '台南市': (120.2513, 22.9999),
        '高雄市': (120.3014, 22.6273),
        '基隆市': (121.7394, 25.1276),
        '新竹市': (120.9647, 24.8138),
        '嘉义市': (120.4473, 23.4801),
        '宜兰县': (121.7195, 24.7021),
        '新竹县': (121.0178, 24.8387),
        '苗栗县': (120.8214, 24.5602),
        '彰化县': (120.5616, 24.0518),
        '南投县': (120.9719, 23.9609),
        '云林县': (120.5434, 23.7092),
        '嘉义县': (120.4491, 23.4518),
        '屏东县': (120.4818, 22.6821),
        '台东县': (121.1444, 22.7972),
        '花莲县': (121.6015, 23.9937),
        '澎湖县': (119.5794, 23.5711),
        '金门县': (118.3186, 24.4324),
        '连江县': (119.5397, 26.1565)
    }
    
    # 从地址中提取城市
    for city, coords in city_coords.items():
        if city in address:
            # 添加一些随机偏移以避免重叠
            import random
            offset_lng = random.uniform(-0.01, 0.01)
            offset_lat = random.uniform(-0.01, 0.01)
            return coords[0] + offset_lng, coords[1] + offset_lat
    
    # 默认返回台北市坐标
    return 121.5654, 25.0330

def create_kml_from_comprehensive_csv():
    """将综合.csv转换为KML格式，按层级/类别分组"""
    
    # 读取CSV文件
    institutions = []
    try:
        with open('综合.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['機構中文全名'].strip() and row['地址'].strip():  # 忽略空行
                    institutions.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '综合.csv'")
        return
    
    print(f"读取到 {len(institutions)} 个机构信息")
    
    # 按层级/类别分组
    categories = {}
    for inst in institutions:
        category = inst['層級/類別'].strip()
        if not category:
            category = '其他'
        if category not in categories:
            categories[category] = []
        categories[category].append(inst)
    
    print(f"分为 {len(categories)} 个分组：")
    for category, insts in categories.items():
        print(f"  - {category}: {len(insts)} 个机构")
    
    # 为每个机构获取坐标
    print("正在获取地理坐标...")
    for inst in institutions:
        lng, lat = geocode_address(inst['地址'])
        inst['经度'] = lng
        inst['纬度'] = lat
    
    # 定义不同类别的样式
    category_styles = {
        '部': {'color': 'ff0000ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/government.png'},
        '會': {'color': 'ff00ff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/offices.png'},
        '獨立行政機關': {'color': 'ffff0000', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '立法機關': {'color': 'ff00ffff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/library.png'},
        '常設委員會': {'color': 'ffff00ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/conference.png'},
        '輔助單位': {'color': 'ffffff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/info.png'},
        '司法機關': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '其他': {'color': 'ff808080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png'}
    }
    
    # 创建KML内容
    kml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>台湾政府机构分布图</name>
    <description>台湾各级政府机构地理位置信息</description>
'''
    
    # 添加样式定义
    for category, style_info in category_styles.items():
        kml_content += f'''
    <Style id="{category}_style">
      <IconStyle>
        <color>{style_info['color']}</color>
        <scale>1.2</scale>
        <Icon>
          <href>{style_info['icon']}</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>{style_info['color']}</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>'''
    
    # 为每个类别创建文件夹
    for category, category_insts in categories.items():
        kml_content += f'''
    <Folder>
      <name>{category} ({len(category_insts)}个)</name>
      <description>{category}类别的政府机构</description>
'''
        
        # 添加该类别的所有机构
        for inst in category_insts:
            # 获取样式，如果没有定义则使用默认样式
            style_key = category if category in category_styles else '其他'
            
            kml_content += f'''
      <Placemark>
        <name>{inst['機構中文全名']}</name>
        <description><![CDATA[
          <table border="1" cellpadding="5" cellspacing="0">
            <tr><td><b>机构中文全名</b></td><td>{inst['機構中文全名']}</td></tr>
            <tr><td><b>机构英文名称</b></td><td>{inst['機構英文名稱']}</td></tr>
            <tr><td><b>层级/类别</b></td><td>{inst['層級/類別']}</td></tr>
            <tr><td><b>地址</b></td><td>{inst['地址']}</td></tr>
            <tr><td><b>上级机关</b></td><td>{inst['上級機關']}</td></tr>
            <tr><td><b>坐标</b></td><td>{inst['经度']:.6f}, {inst['纬度']:.6f}</td></tr>
          </table>
        ]]></description>
        <styleUrl>#{style_key}_style</styleUrl>
        <Point>
          <coordinates>{inst['经度']},{inst['纬度']},0</coordinates>
        </Point>
      </Placemark>'''
        
        kml_content += '''
    </Folder>'''
    
    kml_content += '''
  </Document>
</kml>'''
    
    # 保存KML文件
    output_file = '台湾政府机构分布图.kml'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(kml_content)
    
    print(f"\n✅ 成功创建KML文件: {output_file}")
    print(f"📊 包含 {len(institutions)} 个政府机构，分为 {len(categories)} 个分组")
    
    # 创建统计报告
    create_statistics_report(categories, institutions)

def create_statistics_report(categories, institutions):
    """创建统计报告"""
    with open('政府机构统计报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾政府机构统计报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总机构数：{len(institutions)} 个\n\n")
        
        f.write("按层级/类别统计：\n")
        f.write("-" * 30 + "\n")
        for category, insts in categories.items():
            f.write(f"{category:15} {len(insts):3} 个\n")
        
        f.write("\n详细列表：\n")
        f.write("=" * 50 + "\n")
        
        for category, insts in categories.items():
            f.write(f"\n{category} ({len(insts)}个)\n")
            f.write("-" * 40 + "\n")
            for inst in insts:
                f.write(f"{inst['機構中文全名']:30} {inst['地址']}\n")
    
    print("创建了统计报告文件: 政府机构统计报告.txt")

if __name__ == "__main__":
    create_kml_from_comprehensive_csv()
