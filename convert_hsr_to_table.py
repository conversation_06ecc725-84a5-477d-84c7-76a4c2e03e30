#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import json

def convert_hsr_to_table():
    """将台湾高铁车站数据转换为表格格式"""
    
    # 读取原始CSV文件
    stations = []
    try:
        with open('台湾高速铁路（高铁）车站.txt', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                stations.append({
                    '车站编号': row['id'],
                    '车站名称': row['name'],
                    '邮政编码': row['zipcode'],
                    '地址': row['address'],
                    '纬度': float(row['lat']),
                    '经度': float(row['lon'])
                })
    except FileNotFoundError:
        print("错误：找不到文件 '台湾高速铁路（高铁）车站.txt'")
        return
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return
    
    # 保存为新的CSV文件
    output_csv = '台湾高铁车站信息表.csv'
    with open(output_csv, 'w', newline='', encoding='utf-8-sig') as f:
        fieldnames = ['车站编号', '车站名称', '邮政编码', '地址', '纬度', '经度']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(stations)
    
    print(f'已成功转换 {len(stations)} 个高铁车站信息到CSV文件: {output_csv}')
    
    # 创建文本报告
    create_text_report(stations)
    
    # 创建HTML表格
    create_html_table(stations)
    
    # 创建JSON文件
    with open('台湾高铁车站信息.json', 'w', encoding='utf-8') as f:
        json.dump(stations, f, ensure_ascii=False, indent=2)
    
    print("同时创建了JSON文件: 台湾高铁车站信息.json")

def create_text_report(stations):
    """创建文本报告"""
    with open('台湾高铁车站信息报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾高速铁路（高铁）车站信息报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总车站数：{len(stations)} 个\n\n")
        
        f.write("车站列表（由北到南）：\n")
        f.write("-" * 50 + "\n")
        f.write(f"{'编号':4} {'车站名称':12} {'邮政编码':8} {'纬度':10} {'经度':11}\n")
        f.write("-" * 50 + "\n")
        
        for station in stations:
            f.write(f"{station['车站编号']:4} {station['车站名称']:12} {station['邮政编码']:8} "
                   f"{station['纬度']:10.6f} {station['经度']:11.6f}\n")
        
        f.write("\n详细地址信息：\n")
        f.write("=" * 50 + "\n")
        for station in stations:
            f.write(f"\n{station['车站编号']}. {station['车站名称']}\n")
            f.write(f"   地址：{station['地址']}\n")
            f.write(f"   坐标：{station['纬度']:.6f}, {station['经度']:.6f}\n")
    
    print("创建了文本报告文件: 台湾高铁车站信息报告.txt")

def create_html_table(stations):
    """创建HTML表格文件"""
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台湾高速铁路（高铁）车站信息表</title>
    <style>
        body {{
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }}
        h1 {{
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }}
        .subtitle {{
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }}
        .stats {{
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 10px;
            color: white;
            text-align: center;
        }}
        .stat-number {{
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .route-info {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 5px solid #007bff;
            border-radius: 5px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }}
        th, td {{
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        tr:hover {{
            background-color: #e3f2fd;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }}
        .station-id {{
            font-weight: bold;
            color: #007bff;
            text-align: center;
            font-size: 1.2em;
        }}
        .station-name {{
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }}
        .coordinates {{
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #666;
        }}
        .search-box {{
            margin-bottom: 20px;
            padding: 12px;
            width: 100%;
            max-width: 400px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }}
        .search-box:focus {{
            border-color: #007bff;
        }}
        .search-container {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚄 台湾高速铁路车站信息表</h1>
        <div class="subtitle">Taiwan High Speed Rail Stations</div>
        
        <div class="stats">
            <div class="stat-number">{len(stations)}</div>
            <div>高铁车站总数</div>
        </div>
        
        <div class="route-info">
            <h3>🗺️ 路线信息</h3>
            <p><strong>营运路线：</strong>南港 ↔ 左营（全长345公里）</p>
            <p><strong>营运时间：</strong>2007年1月5日正式通车</p>
            <p><strong>最高时速：</strong>300公里/小时</p>
        </div>
        
        <div class="search-container">
            <input type="text" id="searchBox" class="search-box" placeholder="🔍 搜索车站名称或地址...">
        </div>
        
        <table id="stationTable">
            <thead>
                <tr>
                    <th>车站编号</th>
                    <th>车站名称</th>
                    <th>邮政编码</th>
                    <th>地址</th>
                    <th>纬度</th>
                    <th>经度</th>
                </tr>
            </thead>
            <tbody id="tableBody">
"""
    
    # 添加表格数据
    for station in stations:
        html_content += f"""                <tr>
                    <td class="station-id">{station['车站编号']}</td>
                    <td class="station-name">{station['车站名称']}</td>
                    <td>{station['邮政编码']}</td>
                    <td>{station['地址']}</td>
                    <td class="coordinates">{station['纬度']:.6f}</td>
                    <td class="coordinates">{station['经度']:.6f}</td>
                </tr>
"""
    
    html_content += f"""            </tbody>
        </table>
        
        <div class="footer">
            <p>数据更新时间：2024年 | 共计 {len(stations)} 个高铁车站</p>
            <p>💡 提示：可以使用搜索框快速查找特定车站</p>
        </div>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchBox').addEventListener('input', function() {{
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#tableBody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {{
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {{
                    row.style.display = '';
                    visibleCount++;
                }} else {{
                    row.style.display = 'none';
                }}
            }});
            
            // 更新显示的车站数量
            const statNumber = document.querySelector('.stat-number');
            if (searchTerm) {{
                statNumber.textContent = visibleCount;
                statNumber.parentElement.querySelector('div:last-child').textContent = '搜索结果';
            }} else {{
                statNumber.textContent = {len(stations)};
                statNumber.parentElement.querySelector('div:last-child').textContent = '高铁车站总数';
            }}
        }});
        
        // 添加表格行点击效果
        document.querySelectorAll('#tableBody tr').forEach(row => {{
            row.addEventListener('click', function() {{
                // 可以在这里添加点击行的处理逻辑
                console.log('点击了车站：', this.cells[1].textContent);
            }});
        }});
    </script>
</body>
</html>"""
    
    with open('台湾高铁车站信息表.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("创建了HTML表格文件: 台湾高铁车站信息表.html")

if __name__ == "__main__":
    convert_hsr_to_table()
