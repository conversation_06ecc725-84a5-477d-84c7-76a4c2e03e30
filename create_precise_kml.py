#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import requests
import time
import json

def google_geocode(address, api_key):
    """使用Google Maps Geocoding API获取精确坐标"""
    url = "https://maps.googleapis.com/maps/api/geocode/json"
    params = {
        'address': address,
        'key': api_key,
        'language': 'zh-TW',  # 繁体中文
        'region': 'tw'        # 台湾地区
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if data['status'] == 'OK' and data['results']:
            location = data['results'][0]['geometry']['location']
            formatted_address = data['results'][0]['formatted_address']
            return location['lng'], location['lat'], formatted_address
        else:
            print(f"  Google API错误: {data.get('status', 'Unknown error')}")
            if data.get('error_message'):
                print(f"  错误详情: {data['error_message']}")
            return None, None, None
            
    except Exception as e:
        print(f"  Google API请求失败: {e}")
        return None, None, None

def create_precise_kml_from_csv():
    """使用Google API创建精确坐标的KML文件"""
    
    api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
    
    # 读取CSV文件
    institutions = []
    try:
        with open('综合.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['機構中文全名'].strip() and row['地址'].strip():
                    institutions.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '综合.csv'")
        return
    
    print(f"准备为 {len(institutions)} 个机构获取精确坐标...")
    print("使用Google Maps Geocoding API")
    print("-" * 50)
    
    # 为每个机构获取精确坐标
    success_count = 0
    failed_addresses = []
    
    for i, inst in enumerate(institutions):
        print(f"处理 {i+1:3d}/{len(institutions)}: {inst['機構中文全名']}")
        print(f"  地址: {inst['地址']}")
        
        lng, lat, formatted_address = google_geocode(inst['地址'], api_key)
        
        if lng and lat:
            inst['经度'] = lng
            inst['纬度'] = lat
            inst['格式化地址'] = formatted_address
            success_count += 1
            print(f"  ✅ 成功: {lng:.6f}, {lat:.6f}")
        else:
            # 如果失败，使用台北市中心作为默认坐标
            inst['经度'] = 121.5654
            inst['纬度'] = 25.0330
            inst['格式化地址'] = inst['地址']
            failed_addresses.append(inst['地址'])
            print(f"  ❌ 失败，使用默认坐标")
        
        # 避免请求过快，Google API有速率限制
        time.sleep(0.1)  # 每秒最多10个请求
    
    print(f"\n📊 处理完成:")
    print(f"  成功获取: {success_count}/{len(institutions)} 个精确坐标")
    print(f"  失败数量: {len(failed_addresses)} 个")
    
    if failed_addresses:
        print(f"\n❌ 失败的地址:")
        for addr in failed_addresses:
            print(f"  - {addr}")
    
    # 按层级/类别分组
    categories = {}
    for inst in institutions:
        category = inst['層級/類別'].strip()
        if not category:
            category = '其他'
        if category not in categories:
            categories[category] = []
        categories[category].append(inst)
    
    # 定义不同类别的样式
    category_styles = {
        '部': {'color': 'ff0000ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/government.png'},
        '會': {'color': 'ff00ff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/offices.png'},
        '獨立行政機關': {'color': 'ffff0000', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '立法機關': {'color': 'ff00ffff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/library.png'},
        '常設委員會': {'color': 'ffff00ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/conference.png'},
        '輔助單位': {'color': 'ffffff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/info.png'},
        '最高司法機關': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '最高法院': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '高等法院': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '地方法院': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '專業法院': {'color': 'ff800080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '總統幕僚機關': {'color': 'ff4b0082', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/capital_big.png'},
        '總統諮詢機關': {'color': 'ff4b0082', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/capital_small.png'},
        '總統府直屬機關': {'color': 'ff4b0082', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/capital_big.png'},
        '直轄市議會': {'color': 'ff008080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/town_hall.png'},
        '縣市議會': {'color': 'ff008080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/town_hall.png'},
        '直轄市政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '縣市政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '縣政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '國民運動中心': {'color': 'ff32cd32', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/sports.png'},
        '文化藝術中心': {'color': 'ffda70d6', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/arts.png'},
        '其他': {'color': 'ff808080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png'}
    }
    
    # 创建KML内容
    kml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>台湾政府机构精确分布图</name>
    <description>使用Google Maps API获取的台湾各级政府机构精确地理位置信息</description>
'''
    
    # 添加样式定义
    for category, style_info in category_styles.items():
        kml_content += f'''
    <Style id="{category}_style">
      <IconStyle>
        <color>{style_info['color']}</color>
        <scale>1.2</scale>
        <Icon>
          <href>{style_info['icon']}</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>{style_info['color']}</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>'''
    
    # 为每个类别创建文件夹
    for category, category_insts in categories.items():
        kml_content += f'''
    <Folder>
      <name>{category} ({len(category_insts)}个)</name>
      <description>{category}类别的政府机构</description>
'''
        
        # 添加该类别的所有机构
        for inst in category_insts:
            # 获取样式，如果没有定义则使用默认样式
            style_key = category if category in category_styles else '其他'
            
            kml_content += f'''
      <Placemark>
        <name>{inst['機構中文全名']}</name>
        <description><![CDATA[
          <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><td><b>机构中文全名</b></td><td>{inst['機構中文全名']}</td></tr>
            <tr><td><b>机构英文名称</b></td><td>{inst['機構英文名稱']}</td></tr>
            <tr><td><b>层级/类别</b></td><td>{inst['層級/類別']}</td></tr>
            <tr><td><b>原始地址</b></td><td>{inst['地址']}</td></tr>
            <tr><td><b>格式化地址</b></td><td>{inst['格式化地址']}</td></tr>
            <tr><td><b>上级机关</b></td><td>{inst['上級機關']}</td></tr>
            <tr><td><b>精确坐标</b></td><td>{inst['经度']:.6f}, {inst['纬度']:.6f}</td></tr>
          </table>
          <br/>
          <a href="https://www.google.com/maps/search/?api=1&query={inst['纬度']},{inst['经度']}" target="_blank">📍 在Google地图中查看</a>
        ]]></description>
        <styleUrl>#{style_key}_style</styleUrl>
        <Point>
          <coordinates>{inst['经度']},{inst['纬度']},0</coordinates>
        </Point>
      </Placemark>'''
        
        kml_content += '''
    </Folder>'''
    
    kml_content += '''
  </Document>
</kml>'''
    
    # 保存KML文件
    output_file = '台湾政府机构精确分布图.kml'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(kml_content)
    
    # 保存带坐标的CSV文件
    csv_output_file = '政府机构_精确坐标.csv'
    with open(csv_output_file, 'w', newline='', encoding='utf-8-sig') as f:
        fieldnames = ['機構中文全名', '機構英文名稱', '層級/類別', '地址', '格式化地址', '上級機關', '经度', '纬度']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(institutions)
    
    print(f"\n✅ 文件创建完成:")
    print(f"  📍 KML文件: {output_file}")
    print(f"  📊 CSV文件: {csv_output_file}")
    print(f"  📈 包含 {len(institutions)} 个政府机构，分为 {len(categories)} 个分组")
    
    # 创建详细统计报告
    create_detailed_report(categories, institutions, success_count, failed_addresses)

def create_detailed_report(categories, institutions, success_count, failed_addresses):
    """创建详细的统计报告"""
    with open('政府机构精确坐标统计报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾政府机构精确坐标统计报告\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("📊 总体统计:\n")
        f.write(f"  总机构数: {len(institutions)} 个\n")
        f.write(f"  成功获取精确坐标: {success_count} 个 ({success_count/len(institutions)*100:.1f}%)\n")
        f.write(f"  使用默认坐标: {len(failed_addresses)} 个 ({len(failed_addresses)/len(institutions)*100:.1f}%)\n\n")
        
        f.write("📍 按层级/类别统计:\n")
        f.write("-" * 40 + "\n")
        for category, insts in sorted(categories.items()):
            f.write(f"{category:20} {len(insts):3} 个\n")
        
        if failed_addresses:
            f.write(f"\n❌ 获取坐标失败的地址 ({len(failed_addresses)}个):\n")
            f.write("-" * 40 + "\n")
            for addr in failed_addresses:
                f.write(f"  - {addr}\n")
        
        f.write(f"\n📋 详细机构列表:\n")
        f.write("=" * 60 + "\n")
        
        for category, insts in sorted(categories.items()):
            f.write(f"\n{category} ({len(insts)}个)\n")
            f.write("-" * 50 + "\n")
            for inst in insts:
                f.write(f"{inst['機構中文全名']:30} ")
                f.write(f"({inst['经度']:.6f}, {inst['纬度']:.6f})\n")
                f.write(f"{'':30} {inst['地址']}\n")
    
    print(f"  📄 统计报告: 政府机构精确坐标统计报告.txt")

if __name__ == "__main__":
    print("🗺️  台湾政府机构精确坐标KML生成器")
    print("=" * 50)
    print("使用Google Maps Geocoding API获取精确地理坐标")
    print("API密钥已配置，开始处理...")
    print()
    
    create_precise_kml_from_csv()
