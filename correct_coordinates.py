#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import requests
import time

def dms_to_decimal(degrees, minutes, seconds):
    """将度分秒格式转换为十进制度数"""
    return degrees + minutes/60 + seconds/3600

def decimal_to_dms(decimal_degrees):
    """将十进制度数转换为度分秒格式"""
    degrees = int(decimal_degrees)
    minutes_float = (decimal_degrees - degrees) * 60
    minutes = int(minutes_float)
    seconds = (minutes_float - minutes) * 60
    return degrees, minutes, seconds

class CoordinateCorrector:
    """坐标校正器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        
        # 已知的正确坐标（手动校正）
        self.manual_corrections = {
            '屏東縣議會': {
                'lat': dms_to_decimal(22, 40, 54),  # 22°40'54"N
                'lng': dms_to_decimal(120, 29, 18), # 120°29'18"E
                'source': '手动校正'
            },
            # 可以继续添加其他已知的正确坐标
        }
        
        # 地址别名映射（处理地址变更或多个名称的情况）
        self.address_aliases = {
            '屏東縣議會': [
                '屏東縣屏東市忠孝路145號',
                '屏東縣議會',
                '屏東縣屏東市議會'
            ]
        }
    
    def get_corrected_coordinates(self, institution_name, address):
        """获取校正后的坐标"""

        # 1. 首先检查是否有手动校正的坐标
        if institution_name in self.manual_corrections:
            correction = self.manual_corrections[institution_name]
            print(f"  🔧 使用手动校正坐标 ({correction['source']})")
            return correction['lng'], correction['lat'], '手动校正'

        # 2. 使用机构名称（而不是地址）进行Google API搜索
        lng, lat, formatted_address = self.google_geocode(institution_name)
        
        if lng and lat:
            # 3. 检查是否需要验证（可以添加更多验证逻辑）
            if self.needs_verification(institution_name, lng, lat):
                print(f"  ⚠️  坐标可能需要验证")
                return lng, lat, f'Google API (需验证): {formatted_address}'
            else:
                return lng, lat, f'Google API: {formatted_address}'
        
        return None, None, None
    
    def needs_verification(self, institution_name, lng, lat):
        """检查坐标是否需要人工验证"""
        # 这里可以添加各种验证逻辑
        # 例如：检查是否在合理的地理范围内
        
        # 台湾的大致范围
        taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
        if not (taiwan_bounds['min_lat'] <= lat <= taiwan_bounds['max_lat'] and
                taiwan_bounds['min_lng'] <= lng <= taiwan_bounds['max_lng']):
            return True
        
        return False
    
    def google_geocode(self, search_term):
        """使用Google Maps Geocoding API搜索机构名称"""
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': search_term,  # 现在使用机构名称而不是地址
            'key': self.google_api_key,
            'language': 'zh-TW',
            'region': 'tw'
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                formatted_address = data['results'][0]['formatted_address']
                return location['lng'], location['lat'], formatted_address
            else:
                print(f"  Google API错误: {data.get('status', 'Unknown error')}")
                return None, None, None
                
        except Exception as e:
            print(f"  Google API请求失败: {e}")
            return None, None, None

def create_corrected_kml():
    """创建校正后的KML文件"""
    
    corrector = CoordinateCorrector()
    
    # 读取CSV文件
    institutions = []
    try:
        with open('综合.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['機構中文全名'].strip() and row['地址'].strip():
                    institutions.append(row)
    except FileNotFoundError:
        print("错误：找不到文件 '综合.csv'")
        return
    
    print(f"准备为 {len(institutions)} 个机构获取校正后的坐标...")
    print("🔍 使用机构名称（第一列）作为搜索关键词，而不是地址")
    print("=" * 60)
    
    # 处理每个机构
    success_count = 0
    manual_corrections_used = 0
    verification_needed = []
    
    for i, inst in enumerate(institutions):
        print(f"\n处理 {i+1:3d}/{len(institutions)}: {inst['機構中文全名']}")
        print(f"  搜索关键词: {inst['機構中文全名']}")
        print(f"  地址: {inst['地址']}")
        
        lng, lat, source = corrector.get_corrected_coordinates(
            inst['機構中文全名'],
            inst['地址']  # 地址仍然传递，但现在主要用机构名称搜索
        )
        
        if lng and lat:
            inst['经度'] = lng
            inst['纬度'] = lat
            inst['坐标来源'] = source
            success_count += 1
            
            if '手动校正' in source:
                manual_corrections_used += 1
                print(f"  ✅ 手动校正: {lng:.6f}, {lat:.6f}")
            elif '需验证' in source:
                verification_needed.append(inst['機構中文全名'])
                print(f"  ⚠️  需验证: {lng:.6f}, {lat:.6f}")
            else:
                print(f"  ✅ Google API: {lng:.6f}, {lat:.6f}")
        else:
            # 使用默认坐标
            inst['经度'] = 121.5654
            inst['纬度'] = 25.0330
            inst['坐标来源'] = '默认坐标(台北市中心)'
            print(f"  ❌ 失败，使用默认坐标")
        
        time.sleep(0.1)  # 避免API请求过快
    
    # 显示统计信息
    print(f"\n" + "=" * 60)
    print(f"📊 处理完成统计:")
    print(f"  总机构数: {len(institutions)}")
    print(f"  成功获取坐标: {success_count}")
    print(f"  使用手动校正: {manual_corrections_used}")
    print(f"  需要验证: {len(verification_needed)}")
    
    if verification_needed:
        print(f"\n⚠️  以下机构的坐标可能需要人工验证:")
        for name in verification_needed:
            print(f"  - {name}")
    
    # 按层级/类别分组
    categories = {}
    for inst in institutions:
        category = inst['層級/類別'].strip() or '其他'
        if category not in categories:
            categories[category] = []
        categories[category].append(inst)
    
    # 创建KML文件
    create_kml_file(categories, institutions)
    
    # 保存校正后的CSV文件
    save_corrected_csv(institutions)
    
    # 创建校正报告
    create_correction_report(institutions, manual_corrections_used, verification_needed)

def create_kml_file(categories, institutions):
    """创建KML文件"""
    
    # 样式定义
    category_styles = {
        '部': {'color': 'ff0000ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/government.png'},
        '會': {'color': 'ff00ff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/offices.png'},
        '獨立行政機關': {'color': 'ffff0000', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/courthouse.png'},
        '立法機關': {'color': 'ff00ffff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/library.png'},
        '常設委員會': {'color': 'ffff00ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/conference.png'},
        '輔助單位': {'color': 'ffffff00', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/info.png'},
        '直轄市議會': {'color': 'ff008080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/town_hall.png'},
        '縣市議會': {'color': 'ff008080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/town_hall.png'},
        '直轄市政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '縣市政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '縣政府': {'color': 'ff0080ff', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/city_hall.png'},
        '國民運動中心': {'color': 'ff32cd32', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/sports.png'},
        '文化藝術中心': {'color': 'ffda70d6', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/arts.png'},
        '其他': {'color': 'ff808080', 'icon': 'http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png'}
    }
    
    # KML内容
    kml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>台湾政府机构校正坐标分布图</name>
    <description>包含手动校正的台湾各级政府机构精确地理位置信息</description>
'''
    
    # 添加样式
    for category, style_info in category_styles.items():
        kml_content += f'''
    <Style id="{category}_style">
      <IconStyle>
        <color>{style_info['color']}</color>
        <scale>1.2</scale>
        <Icon>
          <href>{style_info['icon']}</href>
        </Icon>
      </IconStyle>
      <LabelStyle>
        <color>{style_info['color']}</color>
        <scale>0.8</scale>
      </LabelStyle>
    </Style>'''
    
    # 添加分组和机构
    for category, category_insts in categories.items():
        kml_content += f'''
    <Folder>
      <name>{category} ({len(category_insts)}个)</name>
      <description>{category}类别的政府机构</description>
'''
        
        for inst in category_insts:
            style_key = category if category in category_styles else '其他'
            
            # 转换坐标为度分秒格式显示
            lat_dms = decimal_to_dms(abs(inst['纬度']))
            lng_dms = decimal_to_dms(abs(inst['经度']))
            lat_str = f"{lat_dms[0]}°{lat_dms[1]}'{lat_dms[2]:.1f}\"N"
            lng_str = f"{lng_dms[0]}°{lng_dms[1]}'{lng_dms[2]:.1f}\"E"
            
            kml_content += f'''
      <Placemark>
        <name>{inst['機構中文全名']}</name>
        <description><![CDATA[
          <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><td><b>机构名称</b></td><td>{inst['機構中文全名']}</td></tr>
            <tr><td><b>英文名称</b></td><td>{inst['機構英文名稱']}</td></tr>
            <tr><td><b>层级类别</b></td><td>{inst['層級/類別']}</td></tr>
            <tr><td><b>地址</b></td><td>{inst['地址']}</td></tr>
            <tr><td><b>上级机关</b></td><td>{inst['上級機關']}</td></tr>
            <tr><td><b>十进制坐标</b></td><td>{inst['经度']:.6f}, {inst['纬度']:.6f}</td></tr>
            <tr><td><b>度分秒坐标</b></td><td>{lat_str}, {lng_str}</td></tr>
            <tr><td><b>坐标来源</b></td><td>{inst['坐标来源']}</td></tr>
          </table>
          <br/>
          <a href="https://www.google.com/maps/search/?api=1&query={inst['纬度']},{inst['经度']}" target="_blank">📍 在Google地图中查看</a>
        ]]></description>
        <styleUrl>#{style_key}_style</styleUrl>
        <Point>
          <coordinates>{inst['经度']},{inst['纬度']},0</coordinates>
        </Point>
      </Placemark>'''
        
        kml_content += '''
    </Folder>'''
    
    kml_content += '''
  </Document>
</kml>'''
    
    # 保存文件
    with open('台湾政府机构校正坐标分布图.kml', 'w', encoding='utf-8') as f:
        f.write(kml_content)
    
    print(f"\n✅ KML文件已创建: 台湾政府机构校正坐标分布图.kml")

def save_corrected_csv(institutions):
    """保存校正后的CSV文件"""
    with open('政府机构_校正坐标.csv', 'w', newline='', encoding='utf-8-sig') as f:
        fieldnames = ['機構中文全名', '機構英文名稱', '層級/類別', '地址', '上級機關', '经度', '纬度', '坐标来源']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(institutions)
    
    print(f"✅ CSV文件已创建: 政府机构_校正坐标.csv")

def create_correction_report(institutions, manual_corrections_used, verification_needed):
    """创建校正报告"""
    with open('坐标校正报告.txt', 'w', encoding='utf-8') as f:
        f.write("台湾政府机构坐标校正报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"📊 校正统计:\n")
        f.write(f"  总机构数: {len(institutions)}\n")
        f.write(f"  手动校正数: {manual_corrections_used}\n")
        f.write(f"  需要验证数: {len(verification_needed)}\n\n")
        
        if verification_needed:
            f.write("⚠️  需要人工验证的机构:\n")
            f.write("-" * 30 + "\n")
            for name in verification_needed:
                f.write(f"  - {name}\n")
            f.write("\n")
        
        f.write("📋 所有机构坐标信息:\n")
        f.write("=" * 50 + "\n")
        for inst in institutions:
            f.write(f"\n{inst['機構中文全名']}\n")
            f.write(f"  地址: {inst['地址']}\n")
            f.write(f"  坐标: {inst['经度']:.6f}, {inst['纬度']:.6f}\n")
            f.write(f"  来源: {inst['坐标来源']}\n")
    
    print(f"✅ 校正报告已创建: 坐标校正报告.txt")

if __name__ == "__main__":
    print("🔧 台湾政府机构坐标校正器")
    print("=" * 50)
    print("包含手动校正功能，确保坐标准确性")
    print()
    
    create_corrected_kml()
